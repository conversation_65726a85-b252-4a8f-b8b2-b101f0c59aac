
## 1. 参考资源

- [React Native官方文档](https://reactnative.dev/docs/getting-started)
- [React Navigation官方文档](https://reactnavigation.org/docs/getting-started)
- [NativeWind官方文档](https://www.nativewind.dev/quick-starts/react-native-cli)
- [MobX官方文档](https://mobx.js.org/README.html)


# 登录相关页面
1. 一键登录页面
2. 验证码/密码登录
3. 注册页面
4. 找回密码
5. 验证码
6. 设置新密码

# 首页相关页面

1. 搜索页面
2. 查产品
3. 查业绩
4. 查人员
5. 查资质
6. 查专利
7. 查企业

# 详情页相关页面
1. 查产品详情
2. 查业绩详情
3. 查人人员详情
4. 查资质详情
5. 查专利详情


# 所有页面公共一个搜索，下面是选项卡
公司、产品、业绩、人人员、资质、专利
筛选条件：地区、行业、更多筛选、排序

首页下面的bottomNavigator：找客户 企业图谱 关注 我的


公司
地区 行业 更多筛选 排序名称
产品
地区 更多筛选 排序名称
业绩
地区 项目业绩 更多筛选 排序名称
人员
地区 选择证书 项目等级 注册证书
资质
地区 企业资质 更多刷选
专利
专利类型 法律状态 更多筛选 排序名称