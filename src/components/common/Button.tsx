import React from 'react';
import {
  TouchableOpacity,
  Text,
  ActivityIndicator,
  TouchableOpacityProps,
  View,
  StyleSheet,
} from 'react-native';
import LinearGradient from 'react-native-linear-gradient';

export type ButtonType = 'primary' | 'secondary' | 'outline' | 'text';
export type ButtonSize = 'small' | 'medium' | 'large';

export interface ButtonProps extends TouchableOpacityProps {
  type?: ButtonType;
  size?: ButtonSize;
  title: string;
  loading?: boolean;
  disabled?: boolean;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  // 渐变相关属性
  gradient?: boolean;
  gradientColors?: string[];
  disabledGradientColors?: string[];
  height?: number;
  borderRadius?: number;
}

const Button: React.FC<ButtonProps> = ({
  type = 'primary',
  size = 'medium',
  title,
  loading = false,
  disabled = false,
  icon,
  iconPosition = 'left',
  gradient = false,
  gradientColors = ['#819EFF', '#4B74FF'],
  disabledGradientColors = ['#9CA3AF', '#9CA3AF'],
  height = 54,
  borderRadius = 27,
  style,
  ...rest
}) => {
  // 按钮类型样式
  const getTypeStyle = () => {
    switch (type) {
      case 'primary':
        return 'bg-blue-500 border-blue-500';
      case 'secondary':
        return 'bg-gray-500 border-gray-500';
      case 'outline':
        return 'bg-transparent border-blue-500';
      case 'text':
        return 'bg-transparent border-transparent';
      default:
        return 'bg-blue-500 border-blue-500';
    }
  };

  // 按钮大小样式
  const getSizeStyle = () => {
    switch (size) {
      case 'small':
        return 'py-1 px-3';
      case 'medium':
        return 'py-2 px-4';
      case 'large':
        return 'py-3 px-6';
      default:
        return 'py-2 px-4';
    }
  };

  // 文字颜色样式
  const getTextColorStyle = () => {
    if (disabled) {
      return 'text-gray-400';
    }

    switch (type) {
      case 'primary':
      case 'secondary':
        return 'text-white';
      case 'outline':
      case 'text':
        return 'text-blue-500';
      default:
        return 'text-white';
    }
  };

  // 文字大小样式
  const getTextSizeStyle = () => {
    switch (size) {
      case 'small':
        return 'text-sm';
      case 'medium':
        return 'text-base';
      case 'large':
        return 'text-lg';
      default:
        return 'text-base';
    }
  };

  // 禁用样式
  const getDisabledStyle = () => {
    return disabled ? 'opacity-50' : '';
  };

  if (gradient) {
    // 渐变按钮
    const isDisabled = disabled || loading;
    const colors = isDisabled ? disabledGradientColors : gradientColors;

    return (
      <TouchableOpacity
        disabled={isDisabled}
        style={[styles.gradientContainer, { height, borderRadius }, style]}
        activeOpacity={0.8}
        {...rest}
      >
        <LinearGradient
          colors={colors}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={[styles.gradientButton, { borderRadius }]}
        >
          {loading ? (
            <ActivityIndicator color="white" size="small" />
          ) : icon ? (
            <View style={styles.gradientContent}>
              <Text style={styles.gradientText}>{title}</Text>
            </View>
          ) : (
            <Text style={styles.gradientText}>{title}</Text>
          )}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  // 原有的按钮样式
  return (
    <TouchableOpacity
      className={`rounded-md border ${getTypeStyle()} ${getSizeStyle()} ${getDisabledStyle()} flex-row items-center justify-center`}
      disabled={disabled || loading}
      style={style}
      {...rest}
    >
      {loading ? (
        <ActivityIndicator
          size="small"
          color={
            type === 'primary' || type === 'secondary' ? '#FFFFFF' : '#3B82F6'
          }
        />
      ) : (
        <View className="flex-row items-center justify-center">
          {icon && iconPosition === 'left' && (
            <View className="mr-2">{icon}</View>
          )}
          <Text
            className={`${getTextColorStyle()} ${getTextSizeStyle()} text-center font-medium`}
          >
            {title}
          </Text>
          {icon && iconPosition === 'right' && (
            <View className="ml-2">{icon}</View>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  gradientContainer: {
    width: '100%',
    overflow: 'hidden',
    elevation: 5,
    shadowColor: '#6366F1',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.25,
    shadowRadius: 8,
  },
  gradientButton: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  gradientContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },

  gradientText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
    textAlign: 'center',
    includeFontPadding: false,
    textAlignVertical: 'center',
    lineHeight: 18,
  },
  iconLeft: {
    marginRight: 8,
  },
  iconRight: {
    marginLeft: 8,
  },
});

export default Button;
