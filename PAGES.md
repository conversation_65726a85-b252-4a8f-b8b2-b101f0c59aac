# 企业图谱应用 - 页面记录

本文档记录了企业图谱应用中已经创建的所有页面和组件，方便开发者了解项目结构和进度。

## 主页和导航
- 底部标签导航（未直接展示代码）
- 导航类型定义（`src/navigation/types.ts`）
- 导航配置（`src/navigation/index.tsx`）

## 用户账户相关页面
- 个人资料编辑页面（`src/screens/profile/EditProfileScreen.tsx`）
- 个人中心主页（`src/screens/profile/ProfileHomeScreen.tsx`）
- 通知页面（`src/screens/profile/NotificationsScreen.tsx`）
- 反馈页面（`src/screens/profile/FeedbackScreen.tsx`）
- 帮助中心页面（`src/screens/profile/HelpScreen.tsx`）
- 关于我们页面（`src/screens/profile/AboutScreen.tsx`）

## 关注相关页面
- 关注列表页面（`src/screens/follow/FollowScreen.tsx`）
- 关注设置页面（`src/screens/follow/FollowSettingsScreen.tsx`）

## 搜索相关页面
- 搜索页面（`src/screens/search/SearchScreen.tsx`）
- 搜索结果页面（`src/screens/search/SearchResultScreen.tsx`）
- 搜索筛选页面（`src/screens/search/SearchFilterScreen.tsx`）

## 列表页面
- 公司列表页面（`src/screens/company/CompanyListScreen.tsx`）
- 产品列表页面（`src/screens/product/ProductListScreen.tsx`）
- 人员列表页面（`src/screens/person/PersonListScreen.tsx`）
- 业绩列表页面（`src/screens/performance/PerformanceListScreen.tsx`）
- 专利列表页面（`src/screens/patent/PatentListScreen.tsx`）

## 详情页面
- 公司详情页面（`src/screens/detail/CompanyDetailScreen.tsx`）
- 人员详情页面（`src/screens/detail/PersonDetailScreen.tsx`）
- 专利详情页面（`src/screens/detail/PatentDetailScreen.tsx`）
- 资质详情页面（`src/screens/detail/QualificationDetailScreen.tsx`）
- 业绩详情页面（`src/screens/detail/PerformanceDetailScreen.tsx`）
- 项目详情页面（`src/screens/detail/ProjectDetailScreen.tsx`）

## 公共组件
- 头部组件（`src/components/common/Header.tsx`）
- 卡片组件（`src/components/common/Card.tsx`）
- 搜索栏组件（`src/components/common/SearchBar.tsx`）
- 标签页组件（`src/components/common/ScrollTabs.tsx`）
- 按钮组件（`src/components/common/Button.tsx`）
- 轮播图组件（`src/components/common/Carousel.tsx`）
- 网格菜单组件（`src/components/common/GridMenu.tsx`）
- 标签栏组件（`src/components/common/TabBar.tsx`）

## 表单组件
- 输入框组件（`src/components/form/Input.tsx`）
- 验证码输入组件（`src/components/form/VerificationCodeInput.tsx`）

## 列表组件
- 列表项组件（`src/components/list/ListItem.tsx`）

## 模态框组件
- 模态框组件（`src/components/modal/Modal.tsx`）

## 未创建但已在路由中定义的页面

## 待完成的页面
- 登录页面
- 注册页面
- 忘记密码页面
- 设置页面
- 其他页面和功能

## 技术栈
- React Native
- TypeScript
- React Navigation
- NativeWind (Tailwind CSS)

## 项目结构说明
项目采用了模块化的结构，按功能和页面类型组织代码：

```
src/
  ├── api/            # API 请求相关
  ├── assets/         # 静态资源（图片、字体等）
  ├── components/     # 可复用组件
  │   ├── common/     # 通用组件
  │   ├── form/       # 表单相关组件
  │   ├── list/       # 列表相关组件
  │   └── modal/      # 模态框相关组件
  ├── constants/      # 常量定义
  ├── hooks/          # 自定义 Hooks
  ├── navigation/     # 导航相关
  ├── screens/        # 页面组件
  │   ├── auth/       # 认证相关页面
  │   ├── company/    # 公司相关页面
  │   ├── detail/     # 详情页面
  │   ├── follow/     # 关注相关页面
  │   ├── patent/     # 专利相关页面
  │   ├── performance/# 业绩相关页面
  │   ├── person/     # 人员相关页面
  │   ├── product/    # 产品相关页面
  │   ├── profile/    # 个人中心相关页面
  │   └── search/     # 搜索相关页面
  ├── store/          # 状态管理
  ├── types/          # 类型定义
  └── utils/          # 工具函数
``` 